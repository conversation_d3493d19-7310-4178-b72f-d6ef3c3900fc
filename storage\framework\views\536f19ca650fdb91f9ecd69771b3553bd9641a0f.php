<?php $__env->startSection('title', 'Create Auction Listing - Vertigo AMS'); ?>

<?php $__env->startSection('page-title', 'Create Auction Listing'); ?>
<?php $__env->startSection('page-subtitle', 'Create a new live auction listing with scheduled dates and products.'); ?>

<?php $__env->startSection('quick-actions'); ?>
<div class="flex space-x-2">
    <a href="<?php echo e(route('auction-listing.index')); ?>" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        <span class="hidden lg:inline">Back to Listings</span>
        <span class="lg:hidden">Back</span>
    </a>
    <a href="<?php echo e(route('v1.auction-listing.create')); ?>" class="flex items-center bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        <span class="hidden lg:inline">Original Form</span>
        <span class="lg:hidden">Original</span>
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-6xl mx-auto">
    <!-- Progress Steps -->
    <div class="mb-8">
        <nav aria-label="Progress">
            <ol class="flex items-center justify-center space-x-8">
                <li class="flex items-center">
                    <div class="flex items-center justify-center h-10 w-10 rounded-full bg-primary-600">
                        <svg class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <span class="ml-3 text-sm font-medium text-primary-600">Basic Information</span>
                </li>
                <li class="flex items-center">
                    <div class="h-0.5 w-16 bg-primary-600"></div>
                    <div class="flex items-center justify-center h-10 w-10 rounded-full border-2 border-primary-600 bg-white ml-4">
                        <span class="h-3 w-3 rounded-full bg-primary-600"></span>
                    </div>
                    <span class="ml-3 text-sm font-medium text-primary-600">Products & Media</span>
                </li>
                <li class="flex items-center">
                    <div class="h-0.5 w-16 bg-gray-200"></div>
                    <div class="flex items-center justify-center h-10 w-10 rounded-full border-2 border-gray-300 bg-white ml-4">
                        <span class="h-3 w-3 rounded-full bg-transparent"></span>
                    </div>
                    <span class="ml-3 text-sm font-medium text-gray-500">Review & Create</span>
                </li>
            </ol>
        </nav>
    </div>

    <!-- Main Form -->
    <form method="POST" action="<?php echo e(route('auction-listing.store')); ?>" enctype="multipart/form-data" id="auction-form" novalidate>
        <?php echo csrf_field(); ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Basic Information Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-primary-50 to-blue-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-10 w-10 rounded-lg bg-primary-500">
                                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>
                                <p class="text-sm text-gray-600">Essential details for your auction listing</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 space-y-6">
                        <!-- Auction Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Auction Name <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="text"
                                       id="name"
                                       name="name"
                                       value="<?php echo e(old('name')); ?>"
                                       required
                                       maxlength="255"
                                       placeholder="e.g., Monthly Estate Auction - January 2024"
                                       class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-all duration-200 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 focus:ring-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                </div>
                            </div>
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <?php echo e($message); ?>

                                </p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Date Range -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Date From -->
                            <div>
                                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">
                                    Start Date & Time <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <input type="datetime-local"
                                           id="date_from"
                                           name="date_from"
                                           value="<?php echo e(old('date_from', date('Y-m-d\TH:i', strtotime('+1 hour')))); ?>"
                                           required
                                           class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-all duration-200 <?php $__errorArgs = ['date_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 focus:ring-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           min="<?php echo e(date('Y-m-d\TH:i')); ?>">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <?php $__errorArgs = ['date_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600 flex items-center">
                                        <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                        <?php echo e($message); ?>

                                    </p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <p class="mt-2 text-sm text-gray-500 flex items-center">
                                    <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    When the auction will start
                                </p>
                            </div>

                            <!-- Date To -->
                            <div>
                                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">
                                    End Date & Time <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <input type="datetime-local"
                                           id="date_to"
                                           name="date_to"
                                           value="<?php echo e(old('date_to', date('Y-m-d\TH:i', strtotime('+1 day')))); ?>"
                                           required
                                           class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-all duration-200 <?php $__errorArgs = ['date_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 focus:ring-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           min="<?php echo e(date('Y-m-d\TH:i')); ?>">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <?php $__errorArgs = ['date_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600 flex items-center">
                                        <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                        <?php echo e($message); ?>

                                    </p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <p class="mt-2 text-sm text-gray-500 flex items-center">
                                    <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    When the auction will end
                                </p>
                            </div>
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Description
                            </label>
                            <div class="relative">
                                <textarea id="description"
                                          name="description"
                                          rows="4"
                                          maxlength="255"
                                          placeholder="Provide additional details about this auction listing..."
                                          class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-all duration-200 resize-none <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 focus:ring-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('description')); ?></textarea>
                                <div class="absolute top-3 left-0 pl-3 flex items-start pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                                    </svg>
                                </div>
                            </div>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <?php echo e($message); ?>

                                </p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="mt-2 flex justify-between items-center">
                                <p class="text-sm text-gray-500 flex items-center">
                                    <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Optional details about the auction
                                </p>
                                <span class="text-sm text-gray-400" id="description-count">0/255</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Selection Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-10 w-10 rounded-lg bg-green-500">
                                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Product Selection</h3>
                                <p class="text-sm text-gray-600">Choose products to include in this auction</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">

                        <!-- Select Products -->
                        <div>
                            <label for="items" class="block text-sm font-medium text-gray-700 mb-2">
                                Select Products <span class="text-red-500">*</span>
                            </label>
                            <?php if($items->count() > 0): ?>
                            <div class="relative">
                                <select id="items"
                                        name="items[]"
                                        multiple
                                        required
                                        class="block w-full border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 <?php $__errorArgs = ['items'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 focus:ring-red-500 focus:border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                    <?php $selectedItems = old('items', []) ?>
                                    <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($item->id); ?>"
                                            <?php echo e(in_array($item->id, $selectedItems) ? 'selected' : ''); ?>

                                            data-image="<?php echo e($item->image ?? asset('assets/img/placeholder.jpg')); ?>"
                                            data-price="<?php echo e($item->starting_price ? _money($item->starting_price) : ''); ?>">
                                        <?php echo e($item->name); ?><?php if($item->starting_price): ?> - Starting: <?php echo e(_money($item->starting_price)); ?><?php endif; ?>
                                    </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <?php else: ?>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                                <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                <h3 class="mt-4 text-lg font-medium text-gray-900">No available products</h3>
                                <p class="mt-2 text-sm text-gray-500">Create some products first to add them to auctions.</p>
                                <div class="mt-6">
                                    <a href="<?php echo e(route('items.create')); ?>" class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200">
                                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Create Product
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php $__errorArgs = ['items'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <?php echo e($message); ?>

                                </p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <?php $__errorArgs = ['items.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 flex items-center">
                                    <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <?php echo e($message); ?>

                                </p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <p class="mt-2 text-sm text-gray-500 flex items-center">
                                <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Search and select products to include in this live auction
                            </p>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-8">
                <!-- Image Upload Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-10 w-10 rounded-lg bg-purple-500">
                                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Auction Images</h3>
                                <p class="text-sm text-gray-600">Upload promotional images</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6" id="auction-images-app">
                        <image-upload-field
                            label="Auction Images"
                            help-text="Upload auction images. Maximum 10 images, 10MB each."
                            field-name="media[]"
                            :max-count="10"
                            :max-file-size="10000"
                            allowed-ext="png|jpg|jpeg|gif"
                            @files-changed="handleFilesChanged"
                            @add-row="handleAddRow"
                            @size-error="handleSizeError"
                            @extension-error="handleExtensionError"
                        ></image-upload-field>

                        <?php $__errorArgs = ['media'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                                <?php echo e($message); ?>

                            </p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <?php $__errorArgs = ['media.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                                <?php echo e($message); ?>

                            </p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Summary Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-10 w-10 rounded-lg bg-blue-500">
                                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-gray-900">Summary</h3>
                                <p class="text-sm text-gray-600">Review your auction details</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <span class="text-sm font-medium text-gray-600">Type</span>
                            <span class="text-sm text-gray-900 bg-green-100 text-green-800 px-2 py-1 rounded-full">Live Auction</span>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <span class="text-sm font-medium text-gray-600">Products Selected</span>
                            <span class="text-sm text-gray-900" id="selected-count">0</span>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <span class="text-sm font-medium text-gray-600">Images Uploaded</span>
                            <span class="text-sm text-gray-900" id="image-count">0</span>
                        </div>
                        <div class="flex items-center justify-between py-2">
                            <span class="text-sm font-medium text-gray-600">Status</span>
                            <span class="text-sm text-gray-900 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Draft</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Tips Card -->
                <div class="bg-gradient-to-br from-amber-50 to-orange-50 rounded-xl border border-amber-200 overflow-hidden">
                    <div class="p-6">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-amber-800">Quick Tips</h3>
                                <div class="mt-2 text-sm text-amber-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>Set realistic start and end times</li>
                                        <li>Include high-quality images</li>
                                        <li>Verify product starting prices</li>
                                        <li>Write clear descriptions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="mt-8 flex items-center justify-between bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div class="flex items-center space-x-4">
                <a href="<?php echo e(route('auction-listing.index')); ?>"
                   class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    Cancel
                </a>
                <button type="button"
                        class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    Preview
                </button>
            </div>
            <button type="submit"
                    class="inline-flex items-center px-8 py-3 text-sm font-medium text-white bg-gradient-to-r from-primary-500 to-primary-600 border border-transparent rounded-lg hover:from-primary-600 hover:to-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Create Auction Listing
            </button>
        </div>
    </form>

    <!-- Help Section -->
    <div class="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-lg bg-blue-500">
                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-blue-900">Live Auction Guidelines</h3>
                <div class="mt-3 text-sm text-blue-800">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium mb-2">Before Creating:</h4>
                            <ul class="list-disc list-inside space-y-1">
                                <li>Ensure all products have starting prices</li>
                                <li>Verify product images and descriptions</li>
                                <li>Plan realistic auction duration</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2">Best Practices:</h4>
                            <ul class="list-disc list-inside space-y-1">
                                <li>Upload high-quality promotional images</li>
                                <li>Set clear start and end times</li>
                                <li>Group related items together</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const selectedCountSpan = document.getElementById('selected-count');
    const descriptionTextarea = document.getElementById('description');
    const descriptionCount = document.getElementById('description-count');
    const itemsSelect = document.getElementById('items');
    const form = document.getElementById('auction-form');
    const nameInput = document.getElementById('name');
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');

    // Initialize Tom Select for items
    let tomSelect = null;
    if (itemsSelect) {
        tomSelect = new TomSelect(itemsSelect, {
            plugins: ['remove_button'],
            placeholder: 'Search and select products...',
            maxItems: null,
            searchField: ['text'],
            render: {
                option: function(data, escape) {
                    const image = data.image || '/assets/img/placeholder.jpg';
                    const price = data.price ? `<div class="text-xs text-gray-500">Starting: ${escape(data.price)}</div>` : '';
                    return `<div class="flex items-center p-2 hover:bg-gray-50 rounded">
                                <img class="h-8 w-8 rounded-full object-cover mr-3" src="${escape(image)}" alt="${escape(data.text)}" onerror="this.src='/assets/img/placeholder.jpg'" />
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900">${escape(data.text)}</div>
                                    ${price}
                                </div>
                            </div>`;
                },
                item: function(data, escape) {
                    return `<div class="flex items-center">
                                <span class="text-sm">${escape(data.text)}</span>
                            </div>`;
                }
            },
            onInitialize: function() {
                // Clear any validation errors when initialized
                this.wrapper.classList.remove('border-red-300');
            },
            onChange: function() {
                // Clear validation errors when selection changes
                this.wrapper.classList.remove('border-red-300');
                const errorMsg = this.wrapper.parentNode.querySelector('.text-red-600');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }
        });
    }

    // Description character counter
    if (descriptionTextarea && descriptionCount) {
        descriptionTextarea.addEventListener('input', function() {
            const count = this.value.length;
            descriptionCount.textContent = `${count}/255`;

            if (count > 200) {
                descriptionCount.classList.add('text-red-500');
                descriptionCount.classList.remove('text-gray-400');
            } else {
                descriptionCount.classList.remove('text-red-500');
                descriptionCount.classList.add('text-gray-400');
            }

            // Clear validation errors on input
            this.classList.remove('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
            this.classList.add('border-gray-300', 'focus:ring-primary-500', 'focus:border-primary-500');
        });

        // Initial count
        descriptionCount.textContent = `${descriptionTextarea.value.length}/255`;
    }

    // Real-time validation for name field
    if (nameInput) {
        nameInput.addEventListener('input', function() {
            // Clear validation errors on input
            this.classList.remove('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
            this.classList.add('border-gray-300', 'focus:ring-primary-500', 'focus:border-primary-500');

            const errorMsg = this.parentNode.querySelector('.text-red-600.mt-2');
            if (errorMsg) {
                errorMsg.remove();
            }
        });
    }

    // Real-time validation for date fields
    [dateFromInput, dateToInput].forEach(input => {
        if (input) {
            input.addEventListener('change', function() {
                // Clear validation errors on change
                this.classList.remove('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
                this.classList.add('border-gray-300', 'focus:ring-primary-500', 'focus:border-primary-500');

                const errorMsg = this.parentNode.querySelector('.text-red-600.mt-2');
                if (errorMsg) {
                    errorMsg.remove();
                }
            });
        }
    });

    // Initialize Vue app for image upload
    const { createApp } = Vue;

    createApp({
        components: {
            ImageUploadField: window.ImageUploadField
        },
        setup() {
            const handleFilesChanged = (count) => {
                console.log('Auction images count:', count);
            };

            const handleAddRow = (index) => {
                console.log('Added image upload row:', index);
            };

            const handleSizeError = (index, file) => {
                console.log('File size error:', file);
                if (typeof Notyf !== 'undefined') {
                    const notyf = new Notyf({dismissible: true});
                    notyf.error('File size too large. Maximum 10MB allowed.');
                }
            };

            const handleExtensionError = (index, file) => {
                console.log('File extension error:', file);
                if (typeof Notyf !== 'undefined') {
                    const notyf = new Notyf({dismissible: true});
                    notyf.error('Invalid file type. Only PNG, JPG, JPEG, and GIF are allowed.');
                }
            };

            return {
                handleFilesChanged,
                handleAddRow,
                handleSizeError,
                handleExtensionError
            };
        }
    }).mount('#auction-images-app');

    // Items selection counter
    if (tomSelect && selectedCountSpan) {
        function updateSelectedCount() {
            const selected = tomSelect.getValue().length;
            selectedCountSpan.textContent = selected;
        }

        tomSelect.on('change', updateSelectedCount);

        // Initial count
        updateSelectedCount();
    }

    // Form validation enhancement
    if (form) {
        form.addEventListener('submit', function(e) {
            const nameInput = document.getElementById('name');
            const dateFromInput = document.getElementById('date_from');
            const dateToInput = document.getElementById('date_to');

            let hasErrors = false;

            // Clear previous errors
            clearFieldErrors();

            // Validate name
            if (!nameInput.value.trim()) {
                showFieldError(nameInput, 'Auction name is required');
                hasErrors = true;
            } else if (nameInput.value.trim().length > 255) {
                showFieldError(nameInput, 'Auction name must not exceed 255 characters');
                hasErrors = true;
            }

            // Validate dates
            if (!dateFromInput.value) {
                showFieldError(dateFromInput, 'Start date is required');
                hasErrors = true;
            } else {
                const startDate = new Date(dateFromInput.value);
                const now = new Date();
                if (startDate < now) {
                    showFieldError(dateFromInput, 'Start date must be in the future');
                    hasErrors = true;
                }
            }

            if (!dateToInput.value) {
                showFieldError(dateToInput, 'End date is required');
                hasErrors = true;
            }

            // Validate date range
            if (dateFromInput.value && dateToInput.value) {
                const startDate = new Date(dateFromInput.value);
                const endDate = new Date(dateToInput.value);

                if (endDate <= startDate) {
                    showFieldError(dateToInput, 'End date must be after start date');
                    hasErrors = true;
                }

                // Check if auction duration is reasonable (at least 1 hour)
                const duration = endDate - startDate;
                const oneHour = 60 * 60 * 1000;
                if (duration < oneHour) {
                    showFieldError(dateToInput, 'Auction must run for at least 1 hour');
                    hasErrors = true;
                }
            }

            // Validate items selection
            if (tomSelect && tomSelect.getValue().length === 0) {
                showFieldError(itemsSelect, 'Please select at least one product');
                hasErrors = true;
            }

            if (hasErrors) {
                e.preventDefault();
                // Scroll to first error
                const firstError = document.querySelector('.border-red-300');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
    }

    function clearFieldErrors() {
        // Remove all error styling
        const errorFields = document.querySelectorAll('.border-red-300');
        errorFields.forEach(field => {
            field.classList.remove('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
            field.classList.add('border-gray-300', 'focus:ring-primary-500', 'focus:border-primary-500');
        });

        // Remove all error messages
        const errorMessages = document.querySelectorAll('.text-red-600');
        errorMessages.forEach(msg => {
            if (msg.classList.contains('mt-2')) {
                msg.remove();
            }
        });
    }

    function showFieldError(field, message) {
        // Handle TomSelect wrapper
        const targetField = field.classList.contains('ts-wrapper') ? field : field;

        targetField.classList.add('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
        targetField.classList.remove('border-gray-300', 'focus:ring-primary-500', 'focus:border-primary-500');

        // For TomSelect, apply error styling to the control
        if (tomSelect && field === itemsSelect) {
            const tsWrapper = tomSelect.wrapper;
            tsWrapper.classList.add('border-red-300');
            tsWrapper.classList.remove('border-gray-300');
        }

        // Remove existing error message
        const container = field.parentNode;
        const existingError = container.querySelector('.text-red-600.mt-2');
        if (existingError) {
            existingError.remove();
        }

        // Add new error message
        const errorDiv = document.createElement('p');
        errorDiv.className = 'mt-2 text-sm text-red-600 flex items-center';
        errorDiv.innerHTML = `
            <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            ${message}
        `;
        container.appendChild(errorDiv);
    }

    console.log('Enhanced Auction Listing Create page loaded');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.modernized-admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/admin/modernized-auction-listing-create.blade.php ENDPATH**/ ?>